//
//  ContentView.swift
//  demox
//
//  Created by ltt on 2025/9/7.
//

import SwiftUI

struct ContentView: View {
    @State private var selectedCategory = "All"
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.black.ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // 顶部状态栏
                    StatusBarView()
                    
                    ScrollView {
                        VStack(alignment: .leading, spacing: 20) {
                            // 分类标签栏
                            CategoryTabsView(selectedCategory: $selectedCategory)
                            
                            // 音乐卡片网格
                            MusicGridView()
                            
                            // Picked for you 区域
                            PickedForYouView()
                            
                            // New releases for you 区域
                            NewReleasesView()
                        }
                        .padding(.horizontal, 16)
                    }
                    
                    // 底部导航栏
                    BottomTabView()
                }
            }
        }
        .navigationBarHidden(true)
    }
}

// 顶部状态栏
struct StatusBarView: View {
    var body: some View {
        HStack {
            Text("9:41")
                .foregroundColor(.white)
                .font(.system(size: 17, weight: .semibold))
            
            Spacer()
            
            HStack(spacing: 5) {
                // 信号强度
                HStack(spacing: 2) {
                    ForEach(0..<4) { index in
                        Rectangle()
                            .frame(width: 3, height: CGFloat(4 + index * 2))
                            .foregroundColor(.white)
                    }
                }
                
                // WiFi图标
                Image(systemName: "wifi")
                    .foregroundColor(.white)
                    .font(.system(size: 15))
                
                // 电池图标
                Image(systemName: "battery.100")
                    .foregroundColor(.white)
                    .font(.system(size: 20))
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 8)
    }
}

// 分类标签栏
struct CategoryTabsView: View {
    @Binding var selectedCategory: String
    let categories = ["All", "Music", "Podcasts", "Audiobooks"]
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(categories, id: \.self) { category in
                    Button(action: {
                        selectedCategory = category
                    }) {
                        Text(category)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(selectedCategory == category ? .black : .white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(selectedCategory == category ? Color.green : Color.gray.opacity(0.3))
                            )
                    }
                }
            }
            .padding(.horizontal, 16)
        }
    }
}

// 音乐卡片网格
struct MusicGridView: View {
    let musicItems = [
        MusicItem(title: "Brat and it's completely diff...", color: .green, hasImage: false),
        MusicItem(title: "Wicked Official Playlist", color: .green, hasImage: true),
        MusicItem(title: "Gracie Abrams", color: .clear, hasImage: true),
        MusicItem(title: "More Life", color: .clear, hasImage: true),
        MusicItem(title: "DJ", color: .blue, hasImage: false),
        MusicItem(title: "Today's Top Hits", color: .clear, hasImage: true),
        MusicItem(title: "eternal sunshine", color: .clear, hasImage: true),
        MusicItem(title: "Short'n'Sweet", color: .clear, hasImage: true)
    ]
    
    var body: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 2), spacing: 8) {
            ForEach(musicItems.indices, id: \.self) { index in
                MusicCardView(item: musicItems[index])
            }
        }
    }
}

// 音乐卡片
struct MusicCardView: View {
    let item: MusicItem
    
    var body: some View {
        HStack(spacing: 12) {
            // 左侧图片或颜色块
            RoundedRectangle(cornerRadius: 4)
                .fill(item.hasImage ? Color.gray.opacity(0.3) : item.color)
                .frame(width: 50, height: 50)
                .overlay(
                    Group {
                        if item.hasImage {
                            Image(systemName: "music.note")
                                .foregroundColor(.white)
                                .font(.system(size: 20))
                        }
                    }
                )
            
            // 标题
            Text(item.title)
                .foregroundColor(.white)
                .font(.system(size: 14, weight: .medium))
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.gray.opacity(0.2))
        )
    }
}

// Picked for you 区域
struct PickedForYouView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Picked for you")
                .foregroundColor(.white)
                .font(.system(size: 22, weight: .bold))
            
            HStack(spacing: 16) {
                // 播客卡片
                VStack(alignment: .leading, spacing: 8) {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.pink.opacity(0.8))
                        .frame(width: 160, height: 160)
                        .overlay(
                            VStack {
                                Circle()
                                    .fill(Color.pink)
                                    .frame(width: 80, height: 80)
                                    .overlay(
                                        Image(systemName: "mouth")
                                            .foregroundColor(.white)
                                            .font(.system(size: 30))
                                    )
                                Text("Sounds\nLike A\nCult")
                                    .foregroundColor(.white)
                                    .font(.system(size: 16, weight: .bold))
                                    .multilineTextAlignment(.center)
                            }
                        )
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Podcast")
                            .foregroundColor(.gray)
                            .font(.system(size: 12))
                        Text("Sounds Like A Cult")
                            .foregroundColor(.white)
                            .font(.system(size: 16, weight: .semibold))
                        Text("A podcast about the\nmodern-day \"cults\" we\nall follow.")
                            .foregroundColor(.gray)
                            .font(.system(size: 14))
                            .lineLimit(3)
                    }
                }
                
                Spacer()
                
                Button(action: {}) {
                    Image(systemName: "ellipsis")
                        .foregroundColor(.gray)
                        .font(.system(size: 20))
                }
            }
        }
    }
}

// New releases for you 区域
struct NewReleasesView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("New releases for you")
                .foregroundColor(.white)
                .font(.system(size: 22, weight: .bold))
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    ForEach(0..<3) { index in
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 140, height: 140)
                            .overlay(
                                Image(systemName: "music.note")
                                    .foregroundColor(.white)
                                    .font(.system(size: 30))
                            )
                    }
                }
                .padding(.horizontal, 16)
            }
        }
    }
}

// 底部导航栏
struct BottomTabView: View {
    var body: some View {
        HStack {
            TabItemView(icon: "house.fill", title: "Home", isSelected: true)
            Spacer()
            TabItemView(icon: "magnifyingglass", title: "Search", isSelected: false)
            Spacer()
            TabItemView(icon: "rectangle.stack", title: "Your Library", isSelected: false)
            Spacer()
            TabItemView(icon: "plus", title: "Create", isSelected: false)
        }
        .padding(.horizontal, 32)
        .padding(.vertical, 12)
        .background(Color.black)
    }
}

// 底部标签项
struct TabItemView: View {
    let icon: String
    let title: String
    let isSelected: Bool
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .foregroundColor(isSelected ? .white : .gray)
                .font(.system(size: 20))
            
            Text(title)
                .foregroundColor(isSelected ? .white : .gray)
                .font(.system(size: 10))
        }
    }
}

// 数据模型
struct MusicItem {
    let title: String
    let color: Color
    let hasImage: Bool
}

#Preview {
    ContentView()
}
