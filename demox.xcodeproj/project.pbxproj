// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		25F1097B2E6DB97800C03097 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 25F109652E6DB97700C03097 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 25F1096C2E6DB97700C03097;
			remoteInfo = demox;
		};
		25F109852E6DB97800C03097 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 25F109652E6DB97700C03097 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 25F1096C2E6DB97700C03097;
			remoteInfo = demox;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		25F1096D2E6DB97700C03097 /* demox.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = demox.app; sourceTree = BUILT_PRODUCTS_DIR; };
		25F1097A2E6DB97800C03097 /* demoxTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = demoxTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		25F109842E6DB97800C03097 /* demoxUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = demoxUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		25F1096F2E6DB97700C03097 /* demox */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = demox;
			sourceTree = "<group>";
		};
		25F1097D2E6DB97800C03097 /* demoxTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = demoxTests;
			sourceTree = "<group>";
		};
		25F109872E6DB97800C03097 /* demoxUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = demoxUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		25F1096A2E6DB97700C03097 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		25F109772E6DB97800C03097 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		25F109812E6DB97800C03097 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		25F109642E6DB97700C03097 = {
			isa = PBXGroup;
			children = (
				25F1096F2E6DB97700C03097 /* demox */,
				25F1097D2E6DB97800C03097 /* demoxTests */,
				25F109872E6DB97800C03097 /* demoxUITests */,
				25F1096E2E6DB97700C03097 /* Products */,
			);
			sourceTree = "<group>";
		};
		25F1096E2E6DB97700C03097 /* Products */ = {
			isa = PBXGroup;
			children = (
				25F1096D2E6DB97700C03097 /* demox.app */,
				25F1097A2E6DB97800C03097 /* demoxTests.xctest */,
				25F109842E6DB97800C03097 /* demoxUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		25F1096C2E6DB97700C03097 /* demox */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 25F1098E2E6DB97800C03097 /* Build configuration list for PBXNativeTarget "demox" */;
			buildPhases = (
				25F109692E6DB97700C03097 /* Sources */,
				25F1096A2E6DB97700C03097 /* Frameworks */,
				25F1096B2E6DB97700C03097 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				25F1096F2E6DB97700C03097 /* demox */,
			);
			name = demox;
			packageProductDependencies = (
			);
			productName = demox;
			productReference = 25F1096D2E6DB97700C03097 /* demox.app */;
			productType = "com.apple.product-type.application";
		};
		25F109792E6DB97800C03097 /* demoxTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 25F109912E6DB97800C03097 /* Build configuration list for PBXNativeTarget "demoxTests" */;
			buildPhases = (
				25F109762E6DB97800C03097 /* Sources */,
				25F109772E6DB97800C03097 /* Frameworks */,
				25F109782E6DB97800C03097 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				25F1097C2E6DB97800C03097 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				25F1097D2E6DB97800C03097 /* demoxTests */,
			);
			name = demoxTests;
			packageProductDependencies = (
			);
			productName = demoxTests;
			productReference = 25F1097A2E6DB97800C03097 /* demoxTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		25F109832E6DB97800C03097 /* demoxUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 25F109942E6DB97800C03097 /* Build configuration list for PBXNativeTarget "demoxUITests" */;
			buildPhases = (
				25F109802E6DB97800C03097 /* Sources */,
				25F109812E6DB97800C03097 /* Frameworks */,
				25F109822E6DB97800C03097 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				25F109862E6DB97800C03097 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				25F109872E6DB97800C03097 /* demoxUITests */,
			);
			name = demoxUITests;
			packageProductDependencies = (
			);
			productName = demoxUITests;
			productReference = 25F109842E6DB97800C03097 /* demoxUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		25F109652E6DB97700C03097 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					25F1096C2E6DB97700C03097 = {
						CreatedOnToolsVersion = 16.4;
					};
					25F109792E6DB97800C03097 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 25F1096C2E6DB97700C03097;
					};
					25F109832E6DB97800C03097 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 25F1096C2E6DB97700C03097;
					};
				};
			};
			buildConfigurationList = 25F109682E6DB97700C03097 /* Build configuration list for PBXProject "demox" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 25F109642E6DB97700C03097;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 25F1096E2E6DB97700C03097 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				25F1096C2E6DB97700C03097 /* demox */,
				25F109792E6DB97800C03097 /* demoxTests */,
				25F109832E6DB97800C03097 /* demoxUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		25F1096B2E6DB97700C03097 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		25F109782E6DB97800C03097 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		25F109822E6DB97800C03097 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		25F109692E6DB97700C03097 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		25F109762E6DB97800C03097 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		25F109802E6DB97800C03097 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		25F1097C2E6DB97800C03097 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 25F1096C2E6DB97700C03097 /* demox */;
			targetProxy = 25F1097B2E6DB97800C03097 /* PBXContainerItemProxy */;
		};
		25F109862E6DB97800C03097 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 25F1096C2E6DB97700C03097 /* demox */;
			targetProxy = 25F109852E6DB97800C03097 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		25F1098C2E6DB97800C03097 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		25F1098D2E6DB97800C03097 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		25F1098F2E6DB97800C03097 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demox;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		25F109902E6DB97800C03097 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demox;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		25F109922E6DB97800C03097 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demoxTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/demox.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/demox";
			};
			name = Debug;
		};
		25F109932E6DB97800C03097 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demoxTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/demox.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/demox";
			};
			name = Release;
		};
		25F109952E6DB97800C03097 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demoxUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = demox;
			};
			name = Debug;
		};
		25F109962E6DB97800C03097 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demoxUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = demox;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		25F109682E6DB97700C03097 /* Build configuration list for PBXProject "demox" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				25F1098C2E6DB97800C03097 /* Debug */,
				25F1098D2E6DB97800C03097 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		25F1098E2E6DB97800C03097 /* Build configuration list for PBXNativeTarget "demox" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				25F1098F2E6DB97800C03097 /* Debug */,
				25F109902E6DB97800C03097 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		25F109912E6DB97800C03097 /* Build configuration list for PBXNativeTarget "demoxTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				25F109922E6DB97800C03097 /* Debug */,
				25F109932E6DB97800C03097 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		25F109942E6DB97800C03097 /* Build configuration list for PBXNativeTarget "demoxUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				25F109952E6DB97800C03097 /* Debug */,
				25F109962E6DB97800C03097 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 25F109652E6DB97700C03097 /* Project object */;
}
